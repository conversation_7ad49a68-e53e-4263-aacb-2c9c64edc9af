<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>T<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON> Training Assistant</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            color: white;
        }

        .container {
            max-width: 800px;
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .setup-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            align-items: center;
        }

        .setup-group {
            text-align: center;
        }

        .setup-group label {
            display: block;
            margin-bottom: 10px;
            font-weight: bold;
            font-size: 1.1em;
        }

        select, button {
            padding: 10px 15px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        select {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            width: 100%;
        }

        button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            font-weight: bold;
            width: 100%;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }

        .game-area {
            display: none;
            text-align: center;
        }

        .game-area.active {
            display: block;
        }

        .status {
            margin-bottom: 20px;
            font-size: 1.3em;
            font-weight: bold;
            min-height: 60px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
        }

        .board {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 3px;
            max-width: 600px;
            margin: 0 auto 30px;
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 15px;
        }

        .cell {
            aspect-ratio: 1;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 6px;
            font-size: 1.8em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #333;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 60px;
        }

        .cell:hover:not(:disabled) {
            background: rgba(255, 255, 255, 1);
            transform: scale(1.05);
        }

        .cell:disabled {
            cursor: not-allowed;
        }

        .cell.x {
            color: #e74c3c;
        }

        .cell.o {
            color: #3498db;
        }

        .recommended-move {
            background: rgba(46, 204, 113, 0.3) !important;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .controls button {
            width: auto;
            min-width: 120px;
        }

        .mode-indicator {
            background: rgba(255, 255, 255, 0.2);
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .setup-panel {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .cell {
                font-size: 1.2em;
                min-height: 40px;
            }

            .board {
                gap: 2px;
                max-width: 350px;
            }

            h1 {
                font-size: 1.8em;
            }

            .container {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 7x7 Tic-Tac-Toe Training Assistant</h1>
        <p class="text-center mb-6 text-lg opacity-90">Get 4 in a row to win! (Horizontal, Vertical, or Diagonal)</p>

        <div class="setup-panel" id="setupPanel">
            <div class="setup-group">
                <label for="playerSymbol">Your Symbol:</label>
                <select id="playerSymbol">
                    <option value="X">X</option>
                    <option value="O">O</option>
                </select>
            </div>
            
            <div class="setup-group">
                <label for="firstPlayer">Who Goes First:</label>
                <select id="firstPlayer">
                    <option value="player">You</option>
                    <option value="opponent">Opponent</option>
                </select>
            </div>
            
            <div class="setup-group">
                <label>&nbsp;</label>
                <button onclick="startGame()">Start Training</button>
            </div>
        </div>

        <div class="game-area" id="gameArea">
            <div class="mode-indicator" id="modeIndicator"></div>
            <div class="status" id="status"></div>
            
            <div class="board" id="board">
                <!-- Cells will be generated by JavaScript -->
            </div>
            
            <div class="controls">
                <button onclick="resetGame()">New Game</button>
                <button onclick="showSetup()">Change Settings</button>
                <button onclick="getHint()" id="hintButton">Get Hint</button>
                <button onclick="showGameHistory()">Game History</button>
                <button onclick="exportCurrentGame()" id="exportButton" style="display:none;">Export Game</button>
            </div>
        </div>

        <!-- Game History Modal -->
        <div id="historyModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000;">
            <div style="background: white; margin: 5% auto; padding: 20px; width: 90%; max-width: 800px; border-radius: 10px; max-height: 80%; overflow-y: auto; color: #333;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h2 style="margin: 0; color: #333;">Game History & Analysis</h2>
                    <button onclick="closeGameHistory()" style="background: #ff4444; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer;">Close</button>
                </div>
                <div id="historyContent"></div>
            </div>
        </div>
    </div>

    <script>
        // Game state
        let board = Array(49).fill(''); // 7x7 = 49 cells
        let playerSymbol = 'X';
        let opponentSymbol = 'O';
        let currentPlayer = 'X';
        let gameActive = false;
        let playerGoesFirst = true;
        let gameMode = 'training'; // 'training' mode for assistance

        // Game recording system
        let currentGame = null;
        let gameHistory = [];
        let moveHistory = [];
        let gameStartTime = null;

        // Initialize the game
        function initializeBoard() {
            const boardElement = document.getElementById('board');
            boardElement.innerHTML = '';

            for (let i = 0; i < 49; i++) { // 7x7 = 49 cells
                const cell = document.createElement('button');
                cell.className = 'cell';
                cell.onclick = () => handleCellClick(i);
                cell.id = `cell-${i}`;
                cell.title = `Position ${i + 1} (Row ${Math.floor(i/7) + 1}, Col ${(i%7) + 1})`;
                boardElement.appendChild(cell);
            }
        }

        function startGame() {
            // Get settings
            playerSymbol = document.getElementById('playerSymbol').value;
            opponentSymbol = playerSymbol === 'X' ? 'O' : 'X';
            playerGoesFirst = document.getElementById('firstPlayer').value === 'player';

            // Reset game state
            board = Array(49).fill(''); // 7x7 = 49 cells
            currentPlayer = 'X';
            gameActive = true;

            // Initialize game recording
            initializeGameRecording();

            // Show game area
            document.getElementById('setupPanel').style.display = 'none';
            document.getElementById('gameArea').classList.add('active');

            // Initialize board
            initializeBoard();
            updateDisplay();

            // Set initial turn
            if (playerGoesFirst) {
                if (playerSymbol === 'X') {
                    showPlayerTurn();
                } else {
                    currentPlayer = 'O';
                    showPlayerTurn();
                }
            } else {
                if (opponentSymbol === 'X') {
                    currentPlayer = 'X';
                    showOpponentTurn();
                } else {
                    currentPlayer = 'O';
                    showOpponentTurn();
                }
            }
        }

        // Game Recording System
        function initializeGameRecording() {
            gameStartTime = new Date();
            moveHistory = [];
            currentGame = {
                id: Date.now(),
                startTime: gameStartTime,
                playerSymbol: playerSymbol,
                opponentSymbol: opponentSymbol,
                playerGoesFirst: playerGoesFirst,
                moves: [],
                outcome: null,
                finalBoard: null,
                criticalMoves: [],
                aiRecommendations: []
            };

            // Hide export button until game ends
            document.getElementById('exportButton').style.display = 'none';
        }

        function recordMove(position, symbol, isPlayerMove, aiRecommendation = null, followedRecommendation = false) {
            const move = {
                position: position,
                row: Math.floor(position / 7),
                col: position % 7,
                symbol: symbol,
                isPlayerMove: isPlayerMove,
                timestamp: new Date(),
                aiRecommendation: aiRecommendation,
                followedRecommendation: followedRecommendation,
                boardState: [...board] // Copy of board state after this move
            };

            currentGame.moves.push(move);
            moveHistory.push(move);
        }

        function recordAIRecommendation(position, reasoning = '') {
            if (currentGame) {
                currentGame.aiRecommendations.push({
                    position: position,
                    row: Math.floor(position / 7),
                    col: position % 7,
                    timestamp: new Date(),
                    reasoning: reasoning,
                    boardState: [...board]
                });
            }
        }

        // Debug function to test threat detection
        function debugThreatDetection(testBoard, symbol) {
            console.log(`\n=== DEBUGGING THREAT DETECTION FOR ${symbol} ===`);

            // Test the specific scenario from lost game 4
            const threats = findAllCriticalThreats(testBoard, symbol);
            console.log(`Found ${threats.length} critical threats:`, threats);

            threats.forEach(threat => {
                const row = Math.floor(threat / 7);
                const col = threat % 7;
                console.log(`- Threat at position ${threat} (row ${row}, col ${col})`);
            });

            return threats;
        }

        function finishGameRecording(outcome, winner = null) {
            if (!currentGame) return;

            currentGame.endTime = new Date();
            currentGame.duration = currentGame.endTime - currentGame.startTime;
            currentGame.outcome = outcome;
            currentGame.winner = winner;
            currentGame.finalBoard = [...board];

            // Analyze the game for critical moves
            analyzeGameForCriticalMoves();

            // Add to history
            gameHistory.unshift(currentGame); // Add to beginning

            // Keep only last 50 games
            if (gameHistory.length > 50) {
                gameHistory = gameHistory.slice(0, 50);
            }

            // Save to localStorage
            saveGameHistory();

            // Show export button
            document.getElementById('exportButton').style.display = 'inline-block';
        }

        function analyzeGameForCriticalMoves() {
            if (!currentGame || currentGame.outcome === 'draw') return;

            const isPlayerWin = currentGame.winner === playerSymbol;
            currentGame.criticalMoves = [];

            // Find moves where player deviated from AI recommendation
            currentGame.moves.forEach((move, index) => {
                if (move.isPlayerMove && move.aiRecommendation !== null && !move.followedRecommendation) {
                    currentGame.criticalMoves.push({
                        moveIndex: index,
                        type: 'deviation',
                        description: `Deviated from AI recommendation at move ${index + 1}`,
                        playerMove: move.position,
                        recommendedMove: move.aiRecommendation
                    });
                }
            });

            // If player lost, try to find the losing sequence
            if (!isPlayerWin && currentGame.winner) {
                findLosingSequence();
            }
        }

        function findLosingSequence() {
            // Analyze the last few moves to identify where the game was lost
            const moves = currentGame.moves;
            const opponentMoves = moves.filter(m => !m.isPlayerMove);

            if (opponentMoves.length >= 2) {
                const lastOpponentMoves = opponentMoves.slice(-2);
                currentGame.criticalMoves.push({
                    type: 'losing_sequence',
                    description: 'Opponent created winning threat sequence',
                    moves: lastOpponentMoves.map(m => m.position)
                });
            }
        }

        function showSetup() {
            document.getElementById('setupPanel').style.display = 'grid';
            document.getElementById('gameArea').classList.remove('active');
        }

        function resetGame() {
            startGame();
        }

        function handleCellClick(index) {
            if (!gameActive || board[index] !== '') return;

            if (currentPlayer === playerSymbol) {
                // Player's move - check if it follows AI recommendation
                const recommendedMove = getLastAIRecommendation();
                const followedRecommendation = recommendedMove === index;

                makeMove(index, playerSymbol, true, recommendedMove, followedRecommendation);
                if (gameActive) {
                    currentPlayer = opponentSymbol;
                    showOpponentTurn();
                }
            } else {
                // Input opponent's move
                makeMove(index, opponentSymbol, false);
                if (gameActive) {
                    currentPlayer = playerSymbol;
                    showPlayerTurn();
                }
            }
        }

        function getLastAIRecommendation() {
            if (currentGame && currentGame.aiRecommendations.length > 0) {
                return currentGame.aiRecommendations[currentGame.aiRecommendations.length - 1].position;
            }
            return null;
        }

        function makeMove(index, symbol, isPlayerMove = false, aiRecommendation = null, followedRecommendation = false) {
            board[index] = symbol;
            updateDisplay();

            // Record the move
            recordMove(index, symbol, isPlayerMove, aiRecommendation, followedRecommendation);

            const winner = checkWinner();
            if (winner) {
                gameActive = false;
                let outcome, message;

                if (winner === 'tie') {
                    outcome = 'draw';
                    message = "Game Over - It's a tie!";
                } else if (winner === playerSymbol) {
                    outcome = 'win';
                    message = "🎉 You won! Great job!";
                } else {
                    outcome = 'loss';
                    message = "😞 You lost. Check the game analysis to see what happened!";
                }

                updateStatus(message);
                clearRecommendations();
                finishGameRecording(outcome, winner);
                return;
            }
        }



        function showOpponentTurn() {
            clearRecommendations();
            updateStatus("⏳ Input your opponent's move by clicking on the board");
            updateModeIndicator("Opponent's Turn - Click where your opponent played");
        }

        function updateDisplay() {
            for (let i = 0; i < 49; i++) { // 7x7 = 49 cells
                const cell = document.getElementById(`cell-${i}`);
                cell.textContent = board[i];
                cell.className = 'cell';
                if (board[i]) {
                    cell.classList.add(board[i].toLowerCase());
                    cell.disabled = true;
                } else {
                    cell.disabled = false;
                }
            }
        }

        function updateStatus(message) {
            document.getElementById('status').innerHTML = message;
        }

        function updateModeIndicator(message) {
            document.getElementById('modeIndicator').textContent = message;
        }

        function clearRecommendations() {
            document.querySelectorAll('.cell').forEach(cell => {
                cell.classList.remove('recommended-move');
            });
        }

        function getHint() {
            if (!gameActive || currentPlayer !== playerSymbol) return;

            clearRecommendations();
            updateStatus("🤔 Calculating best move...");

            setTimeout(() => {
                const bestMove = getBestMove(board, playerSymbol);

                if (bestMove !== -1) {
                    document.getElementById(`cell-${bestMove}`).classList.add('recommended-move');
                    updateStatus(`💡 Hint: Best move is position ${bestMove + 1} (Row ${Math.floor(bestMove/7) + 1}, Col ${(bestMove%7) + 1})`);
                } else {
                    updateStatus("💡 No optimal move found - any move should work!");
                }
            }, 100);
        }

        // Advanced AI with comprehensive threat detection
        function getBestMove(currentBoard, symbol) {
            const opponent = symbol === 'X' ? 'O' : 'X';

            // PRIORITY 1: Check if we can win immediately
            for (let i = 0; i < 49; i++) {
                if (currentBoard[i] === '') {
                    currentBoard[i] = symbol;
                    if (checkWinner(currentBoard) === symbol) {
                        currentBoard[i] = '';
                        return i;
                    }
                    currentBoard[i] = '';
                }
            }

            // PRIORITY 2: Block immediate opponent wins (most critical)
            for (let i = 0; i < 49; i++) {
                if (currentBoard[i] === '') {
                    currentBoard[i] = opponent;
                    if (checkWinner(currentBoard) === opponent) {
                        currentBoard[i] = '';
                        return i;
                    }
                    currentBoard[i] = '';
                }
            }

            // PRIORITY 3: Find ALL critical threats and handle them
            const allThreats = findAllCriticalThreats(currentBoard, opponent);
            if (allThreats.length > 0) {
                // If multiple threats, we might be in trouble, but block the first one
                return allThreats[0];
            }

            // PRIORITY 4: Create our own threats
            const ourThreats = findAllCriticalThreats(currentBoard, symbol);
            if (ourThreats.length > 0) {
                return ourThreats[0];
            }

            // PRIORITY 5: Use minimax for strategic positioning
            const depth = getOptimalDepth(currentBoard);
            const result = minimax(currentBoard, depth, true, symbol, -Infinity, Infinity);
            return result.move;
        }

        // Comprehensive threat detection that finds ALL 3-in-a-row patterns
        function findAllCriticalThreats(currentBoard, symbol) {
            const threats = [];

            // Check all possible 4-in-a-row lines on the board
            for (let row = 0; row < 7; row++) {
                for (let col = 0; col < 7; col++) {
                    // Horizontal lines (4 consecutive in same row)
                    if (col <= 3) {
                        const threat = checkLineForThreat(currentBoard, row, col, 0, 1, symbol);
                        if (threat !== -1) threats.push(threat);
                    }

                    // Vertical lines (4 consecutive in same column)
                    if (row <= 3) {
                        const threat = checkLineForThreat(currentBoard, row, col, 1, 0, symbol);
                        if (threat !== -1) threats.push(threat);
                    }

                    // Diagonal down-right
                    if (row <= 3 && col <= 3) {
                        const threat = checkLineForThreat(currentBoard, row, col, 1, 1, symbol);
                        if (threat !== -1) threats.push(threat);
                    }

                    // Diagonal down-left
                    if (row <= 3 && col >= 3) {
                        const threat = checkLineForThreat(currentBoard, row, col, 1, -1, symbol);
                        if (threat !== -1) threats.push(threat);
                    }
                }
            }

            // Remove duplicates
            return [...new Set(threats)];
        }

        // Check a specific 4-position line for 3-in-a-row threats
        function checkLineForThreat(currentBoard, startRow, startCol, deltaRow, deltaCol, symbol) {
            let symbolCount = 0;
            let emptyCount = 0;
            let emptyPos = -1;
            let positions = [];

            // Check the 4 positions in this line
            for (let i = 0; i < 4; i++) {
                const row = startRow + i * deltaRow;
                const col = startCol + i * deltaCol;

                // Make sure we're still on the board
                if (row < 0 || row >= 7 || col < 0 || col >= 7) {
                    return -1;
                }

                const pos = row * 7 + col;
                positions.push(pos);

                if (currentBoard[pos] === symbol) {
                    symbolCount++;
                } else if (currentBoard[pos] === '') {
                    emptyCount++;
                    emptyPos = pos;
                } else {
                    // Blocked by opponent
                    return -1;
                }
            }

            // If we have exactly 3 of the symbol and 1 empty, this is a critical threat
            if (symbolCount === 3 && emptyCount === 1) {
                return emptyPos;
            }

            return -1;
        }

        function getOptimalDepth(currentBoard) {
            const emptyCount = currentBoard.filter(cell => cell === '').length;

            // Adjust depth based on game stage
            if (emptyCount > 40) return 4; // Early game - moderate depth
            if (emptyCount > 30) return 5; // Mid game - deeper analysis
            if (emptyCount > 20) return 6; // Late mid game - even deeper
            return 7; // End game - maximum depth
        }

        function minimax(currentBoard, depth, isMaximizing, playerSymbol, alpha, beta) {
            const winner = checkWinner(currentBoard);

            // Terminal states
            if (winner === playerSymbol) return { score: 1000 + depth, move: -1 };
            if (winner && winner !== playerSymbol) return { score: -1000 - depth, move: -1 };
            if (winner === 'tie') return { score: 0, move: -1 };
            if (depth === 0) return { score: evaluateBoardPosition(currentBoard, playerSymbol), move: -1 };

            const opponent = playerSymbol === 'X' ? 'O' : 'X';
            const currentSymbol = isMaximizing ? playerSymbol : opponent;

            let bestMove = -1;
            let bestScore = isMaximizing ? -Infinity : Infinity;

            // Get moves ordered by priority (better moves first for alpha-beta pruning)
            const moves = getPrioritizedMoves(currentBoard, currentSymbol);

            for (let move of moves) {
                currentBoard[move] = currentSymbol;
                const result = minimax(currentBoard, depth - 1, !isMaximizing, playerSymbol, alpha, beta);
                currentBoard[move] = '';

                if (isMaximizing) {
                    if (result.score > bestScore) {
                        bestScore = result.score;
                        bestMove = move;
                    }
                    alpha = Math.max(alpha, result.score);
                } else {
                    if (result.score < bestScore) {
                        bestScore = result.score;
                        bestMove = move;
                    }
                    beta = Math.min(beta, result.score);
                }

                // Alpha-beta pruning
                if (beta <= alpha) break;
            }

            return { score: bestScore, move: bestMove };
        }

        function getPrioritizedMoves(currentBoard, symbol) {
            const moves = [];
            const opponent = symbol === 'X' ? 'O' : 'X';

            // Categorize moves by priority
            const winningMoves = [];
            const blockingMoves = [];
            const threatMoves = [];
            const centerMoves = [];
            const otherMoves = [];

            for (let i = 0; i < 49; i++) {
                if (currentBoard[i] !== '') continue;

                // Check if this move wins immediately
                currentBoard[i] = symbol;
                if (checkWinner(currentBoard) === symbol) {
                    winningMoves.push(i);
                    currentBoard[i] = '';
                    continue;
                }
                currentBoard[i] = '';

                // Check if this move blocks opponent win
                currentBoard[i] = opponent;
                if (checkWinner(currentBoard) === opponent) {
                    blockingMoves.push(i);
                    currentBoard[i] = '';
                    continue;
                }
                currentBoard[i] = '';

                // Check if this move creates threats
                const threatScore = evaluatePosition(currentBoard, i, symbol);
                if (threatScore > 100) {
                    threatMoves.push({ move: i, score: threatScore });
                } else {
                    const row = Math.floor(i / 7);
                    const col = i % 7;
                    const centerDistance = Math.abs(row - 3) + Math.abs(col - 3);

                    if (centerDistance <= 2) {
                        centerMoves.push(i);
                    } else {
                        otherMoves.push(i);
                    }
                }
            }

            // Sort threat moves by score
            threatMoves.sort((a, b) => b.score - a.score);

            // Return prioritized move list
            return [
                ...winningMoves,
                ...blockingMoves,
                ...threatMoves.map(t => t.move),
                ...centerMoves,
                ...otherMoves
            ];
        }

        function evaluateBoardPosition(currentBoard, playerSymbol) {
            let score = 0;
            const opponent = playerSymbol === 'X' ? 'O' : 'X';

            // Evaluate all possible 4-in-a-row lines
            for (let row = 0; row < 7; row++) {
                for (let col = 0; col < 7; col++) {
                    const directions = [[0, 1], [1, 0], [1, 1], [1, -1]];

                    for (let [dRow, dCol] of directions) {
                        if (row + 3 * dRow >= 0 && row + 3 * dRow < 7 &&
                            col + 3 * dCol >= 0 && col + 3 * dCol < 7) {

                            let playerCount = 0;
                            let opponentCount = 0;
                            let emptyCount = 0;

                            for (let i = 0; i < 4; i++) {
                                const pos = (row + i * dRow) * 7 + (col + i * dCol);
                                if (currentBoard[pos] === playerSymbol) playerCount++;
                                else if (currentBoard[pos] === opponent) opponentCount++;
                                else emptyCount++;
                            }

                            // Score this line
                            if (opponentCount === 0) {
                                if (playerCount === 3) score += 50;
                                else if (playerCount === 2) score += 10;
                                else if (playerCount === 1) score += 1;
                            }

                            if (playerCount === 0) {
                                if (opponentCount === 3) score -= 50;
                                else if (opponentCount === 2) score -= 10;
                                else if (opponentCount === 1) score -= 1;
                            }
                        }
                    }
                }
            }

            return score;
        }

        // Add thinking indicator for AI calculations
        function showPlayerTurn() {
            clearRecommendations();
            updateStatus("🤔 AI is calculating the optimal move...");
            updateModeIndicator("Analyzing position - please wait");

            // Use setTimeout to allow UI to update before heavy calculation
            setTimeout(() => {
                const bestMove = getBestMove(board, playerSymbol);

                if (bestMove !== -1) {
                    // Generate detailed reasoning
                    const reasoning = generateMoveReasoning(board, bestMove, playerSymbol);

                    // Record AI recommendation with detailed reasoning
                    recordAIRecommendation(bestMove, reasoning);

                    document.getElementById(`cell-${bestMove}`).classList.add('recommended-move');
                    updateStatus(`🎯 Your turn! Recommended move: Position ${bestMove + 1} (Row ${Math.floor(bestMove/7) + 1}, Col ${(bestMove%7) + 1})`);
                } else {
                    updateStatus("🎯 Your turn! Make your move.");
                }

                updateModeIndicator("Your Turn - AI suggests the optimal move");
            }, 100);
        }

        function generateMoveReasoning(currentBoard, move, symbol) {
            const opponent = symbol === 'X' ? 'O' : 'X';

            // Check if this move wins
            currentBoard[move] = symbol;
            if (checkWinner(currentBoard) === symbol) {
                currentBoard[move] = '';
                return "WINNING MOVE - Completes 4 in a row";
            }
            currentBoard[move] = '';

            // Check if this move blocks opponent win
            currentBoard[move] = opponent;
            if (checkWinner(currentBoard) === opponent) {
                currentBoard[move] = '';
                return "CRITICAL BLOCK - Prevents opponent from winning";
            }
            currentBoard[move] = '';

            // Check if this blocks a critical threat
            const opponentThreats = findAllCriticalThreats(currentBoard, opponent);
            if (opponentThreats.includes(move)) {
                return "THREAT BLOCK - Prevents opponent 3-in-a-row from becoming 4";
            }

            // Check if this creates our own threat
            const ourThreats = findAllCriticalThreats(currentBoard, symbol);
            if (ourThreats.includes(move)) {
                return "THREAT CREATION - Creates 3-in-a-row opportunity";
            }

            return "STRATEGIC POSITIONING - Best move by minimax analysis";
        }

        function evaluatePosition(currentBoard, position, symbol) {
            let score = 0;
            const row = Math.floor(position / 7);
            const col = position % 7;

            // Check all directions for potential lines
            const directions = [
                [0, 1],   // horizontal
                [1, 0],   // vertical
                [1, 1],   // diagonal \
                [1, -1]   // diagonal /
            ];

            for (let [dRow, dCol] of directions) {
                score += evaluateDirection(currentBoard, row, col, dRow, dCol, symbol);
            }

            // Bonus for center positions
            const centerDistance = Math.abs(row - 3) + Math.abs(col - 3);
            score += (6 - centerDistance) * 2;

            return score;
        }

        function evaluateDirection(currentBoard, row, col, dRow, dCol, symbol) {
            let score = 0;

            // Check all possible 4-in-a-row lines that include this position
            for (let offset = -3; offset <= 0; offset++) {
                const startRow = row + offset * dRow;
                const startCol = col + offset * dCol;

                // Check if this 4-position line is valid
                if (startRow < 0 || startRow + 3 * dRow >= 7 || startRow + 3 * dRow < 0 ||
                    startCol < 0 || startCol + 3 * dCol >= 7 || startCol + 3 * dCol < 0) {
                    continue;
                }

                let ourCount = 0;
                let emptyCount = 0;
                let opponentCount = 0;

                // Check the 4 positions in this line
                for (let i = 0; i < 4; i++) {
                    const checkRow = startRow + i * dRow;
                    const checkCol = startCol + i * dCol;
                    const pos = checkRow * 7 + checkCol;

                    if (currentBoard[pos] === symbol) {
                        ourCount++;
                    } else if (currentBoard[pos] === '') {
                        emptyCount++;
                    } else {
                        opponentCount++;
                    }
                }

                // Only score if line is not blocked by opponent
                if (opponentCount === 0) {
                    if (ourCount === 3 && emptyCount === 1) {
                        score += 1000; // Almost winning
                    } else if (ourCount === 2 && emptyCount === 2) {
                        score += 100; // Good potential
                    } else if (ourCount === 1 && emptyCount === 3) {
                        score += 10; // Some potential
                    }
                }
            }

            return score;
        }

        function checkWinner(boardToCheck = board) {
            // Check for 4 in a row in any direction
            for (let row = 0; row < 7; row++) {
                for (let col = 0; col < 7; col++) {
                    const pos = row * 7 + col;
                    const symbol = boardToCheck[pos];

                    if (!symbol) continue;

                    // Check horizontal (right)
                    if (col <= 3) {
                        let count = 0;
                        for (let i = 0; i < 4; i++) {
                            if (boardToCheck[row * 7 + col + i] === symbol) count++;
                        }
                        if (count === 4) return symbol;
                    }

                    // Check vertical (down) - FIXED: This was the main issue
                    if (row <= 3) {
                        let count = 0;
                        for (let i = 0; i < 4; i++) {
                            if (boardToCheck[(row + i) * 7 + col] === symbol) count++;
                        }
                        if (count === 4) return symbol;
                    }

                    // Check diagonal (down-right)
                    if (row <= 3 && col <= 3) {
                        let count = 0;
                        for (let i = 0; i < 4; i++) {
                            if (boardToCheck[(row + i) * 7 + col + i] === symbol) count++;
                        }
                        if (count === 4) return symbol;
                    }

                    // Check diagonal (down-left)
                    if (row <= 3 && col >= 3) {
                        let count = 0;
                        for (let i = 0; i < 4; i++) {
                            if (boardToCheck[(row + i) * 7 + col - i] === symbol) count++;
                        }
                        if (count === 4) return symbol;
                    }
                }
            }

            // Check for tie
            if (boardToCheck.every(cell => cell !== '')) {
                return 'tie';
            }

            return null;
        }

        // Enhanced threat detection specifically for the patterns found in lost games
        function findCriticalVerticalThreats(currentBoard, symbol) {
            // Check each column for 3-in-a-row that can become 4
            for (let col = 0; col < 7; col++) {
                for (let startRow = 0; startRow <= 3; startRow++) {
                    let symbolCount = 0;
                    let emptyCount = 0;
                    let emptyPos = -1;

                    // Check 4 consecutive positions in this column
                    for (let i = 0; i < 4; i++) {
                        const pos = (startRow + i) * 7 + col;
                        if (currentBoard[pos] === symbol) {
                            symbolCount++;
                        } else if (currentBoard[pos] === '') {
                            emptyCount++;
                            emptyPos = pos;
                        } else {
                            break; // Blocked by opponent
                        }
                    }

                    // If we found 3 of the symbol and 1 empty, this is critical!
                    if (symbolCount === 3 && emptyCount === 1) {
                        return emptyPos;
                    }
                }
            }
            return -1;
        }

        function findCriticalHorizontalThreats(currentBoard, symbol) {
            // Check each row for 3-in-a-row that can become 4
            for (let row = 0; row < 7; row++) {
                for (let startCol = 0; startCol <= 3; startCol++) {
                    let symbolCount = 0;
                    let emptyCount = 0;
                    let emptyPos = -1;
                    let blocked = false;

                    // Check 4 consecutive positions in this row
                    for (let i = 0; i < 4; i++) {
                        const pos = row * 7 + (startCol + i);
                        if (currentBoard[pos] === symbol) {
                            symbolCount++;
                        } else if (currentBoard[pos] === '') {
                            emptyCount++;
                            emptyPos = pos;
                        } else {
                            // Hit opponent piece - this line is blocked
                            blocked = true;
                            break;
                        }
                    }

                    // If we found 3 of the symbol and 1 empty (and not blocked), this is critical!
                    if (!blocked && symbolCount === 3 && emptyCount === 1) {
                        return emptyPos;
                    }
                }
            }
            return -1;
        }

        function findCriticalDiagonalThreats(currentBoard, symbol) {
            // Check diagonal threats (both directions)
            for (let row = 0; row <= 3; row++) {
                for (let col = 0; col <= 3; col++) {
                    // Check down-right diagonal
                    let symbolCount = 0;
                    let emptyCount = 0;
                    let emptyPos = -1;

                    for (let i = 0; i < 4; i++) {
                        const pos = (row + i) * 7 + (col + i);
                        if (currentBoard[pos] === symbol) {
                            symbolCount++;
                        } else if (currentBoard[pos] === '') {
                            emptyCount++;
                            emptyPos = pos;
                        } else {
                            break;
                        }
                    }

                    if (symbolCount === 3 && emptyCount === 1) {
                        return emptyPos;
                    }
                }

                // Check down-left diagonal
                for (let col = 3; col < 7; col++) {
                    let symbolCount = 0;
                    let emptyCount = 0;
                    let emptyPos = -1;

                    for (let i = 0; i < 4; i++) {
                        const pos = (row + i) * 7 + (col - i);
                        if (currentBoard[pos] === symbol) {
                            symbolCount++;
                        } else if (currentBoard[pos] === '') {
                            emptyCount++;
                            emptyPos = pos;
                        } else {
                            break;
                        }
                    }

                    if (symbolCount === 3 && emptyCount === 1) {
                        return emptyPos;
                    }
                }
            }
            return -1;
        }

        // Game History and Export Functions
        function saveGameHistory() {
            try {
                localStorage.setItem('tictactoe_game_history', JSON.stringify(gameHistory));
            } catch (e) {
                console.warn('Could not save game history to localStorage:', e);
            }
        }

        function loadGameHistory() {
            try {
                const saved = localStorage.getItem('tictactoe_game_history');
                if (saved) {
                    gameHistory = JSON.parse(saved);
                }
            } catch (e) {
                console.warn('Could not load game history from localStorage:', e);
                gameHistory = [];
            }
        }

        function showGameHistory() {
            const modal = document.getElementById('historyModal');
            const content = document.getElementById('historyContent');

            if (gameHistory.length === 0) {
                content.innerHTML = '<p style="text-align: center; color: #666;">No games recorded yet. Play some games to see your history!</p>';
            } else {
                content.innerHTML = generateHistoryHTML();
            }

            modal.style.display = 'block';
        }

        function closeGameHistory() {
            document.getElementById('historyModal').style.display = 'none';
        }

        function generateHistoryHTML() {
            const stats = calculateStats();
            let html = `
                <div style="background: #f0f0f0; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                    <h3 style="margin: 0 0 10px 0; color: #333;">Statistics</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px;">
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: #4CAF50;">${stats.wins}</div>
                            <div style="font-size: 12px; color: #666;">Wins</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: #f44336;">${stats.losses}</div>
                            <div style="font-size: 12px; color: #666;">Losses</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: #FF9800;">${stats.draws}</div>
                            <div style="font-size: 12px; color: #666;">Draws</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: #2196F3;">${stats.winRate}%</div>
                            <div style="font-size: 12px; color: #666;">Win Rate</div>
                        </div>
                    </div>
                </div>

                <h3 style="color: #333; margin-bottom: 15px;">Recent Games</h3>
                <div style="max-height: 400px; overflow-y: auto;">
            `;

            gameHistory.forEach((game, index) => {
                const outcome = game.outcome === 'win' ? '🎉 Win' :
                              game.outcome === 'loss' ? '😞 Loss' : '🤝 Draw';
                const outcomeColor = game.outcome === 'win' ? '#4CAF50' :
                                   game.outcome === 'loss' ? '#f44336' : '#FF9800';

                html += `
                    <div style="border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 10px; background: white;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <div>
                                <span style="font-weight: bold; color: ${outcomeColor};">${outcome}</span>
                                <span style="color: #666; margin-left: 10px;">
                                    ${new Date(game.startTime).toLocaleDateString()} ${new Date(game.startTime).toLocaleTimeString()}
                                </span>
                            </div>
                            <button onclick="viewGameDetails(${index})" style="background: #2196F3; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; font-size: 12px;">
                                View Details
                            </button>
                        </div>
                        <div style="font-size: 14px; color: #666;">
                            You: ${game.playerSymbol} | Opponent: ${game.opponentSymbol} |
                            ${game.playerGoesFirst ? 'You went first' : 'Opponent went first'} |
                            ${game.moves.length} moves | ${Math.round(game.duration / 1000)}s
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            return html;
        }

        function calculateStats() {
            const wins = gameHistory.filter(g => g.outcome === 'win').length;
            const losses = gameHistory.filter(g => g.outcome === 'loss').length;
            const draws = gameHistory.filter(g => g.outcome === 'draw').length;
            const total = gameHistory.length;
            const winRate = total > 0 ? Math.round((wins / total) * 100) : 0;

            return { wins, losses, draws, total, winRate };
        }

        function viewGameDetails(gameIndex) {
            const game = gameHistory[gameIndex];
            const modal = document.getElementById('historyModal');
            const content = document.getElementById('historyContent');

            content.innerHTML = generateGameDetailsHTML(game, gameIndex);
        }

        function generateGameDetailsHTML(game, gameIndex) {
            let html = `
                <div style="margin-bottom: 20px;">
                    <button onclick="showGameHistory()" style="background: #666; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; margin-bottom: 15px;">
                        ← Back to History
                    </button>
                    <h3 style="color: #333; margin: 0;">Game Details</h3>
                </div>

                <div style="background: #f0f0f0; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        <div>
                            <strong>Outcome:</strong> ${game.outcome === 'win' ? '🎉 Victory' : game.outcome === 'loss' ? '😞 Defeat' : '🤝 Draw'}<br>
                            <strong>Duration:</strong> ${Math.round(game.duration / 1000)} seconds<br>
                            <strong>Total Moves:</strong> ${game.moves.length}
                        </div>
                        <div>
                            <strong>You played:</strong> ${game.playerSymbol}<br>
                            <strong>First player:</strong> ${game.playerGoesFirst ? 'You' : 'Opponent'}<br>
                            <strong>Date:</strong> ${new Date(game.startTime).toLocaleString()}
                        </div>
                    </div>
                </div>
            `;

            // Critical moves analysis
            if (game.criticalMoves.length > 0) {
                html += `
                    <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                        <h4 style="color: #856404; margin: 0 0 10px 0;">⚠️ Critical Moves Analysis</h4>
                `;

                game.criticalMoves.forEach(critical => {
                    if (critical.type === 'deviation') {
                        html += `
                            <div style="margin-bottom: 10px; padding: 10px; background: white; border-radius: 5px;">
                                <strong>Move ${critical.moveIndex + 1}:</strong> You deviated from AI recommendation<br>
                                <span style="color: #666;">Your move: Position ${critical.playerMove + 1} | AI suggested: Position ${critical.recommendedMove + 1}</span>
                            </div>
                        `;
                    } else if (critical.type === 'losing_sequence') {
                        html += `
                            <div style="margin-bottom: 10px; padding: 10px; background: white; border-radius: 5px;">
                                <strong>Losing Sequence:</strong> ${critical.description}<br>
                                <span style="color: #666;">Key opponent moves: ${critical.moves.map(m => `Position ${m + 1}`).join(', ')}</span>
                            </div>
                        `;
                    }
                });

                html += '</div>';
            }

            // Move history
            html += `
                <h4 style="color: #333; margin-bottom: 15px;">Move History</h4>
                <div style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; border-radius: 8px;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead style="background: #f8f9fa; position: sticky; top: 0;">
                            <tr>
                                <th style="padding: 10px; text-align: left; border-bottom: 1px solid #ddd;">#</th>
                                <th style="padding: 10px; text-align: left; border-bottom: 1px solid #ddd;">Player</th>
                                <th style="padding: 10px; text-align: left; border-bottom: 1px solid #ddd;">Position</th>
                                <th style="padding: 10px; text-align: left; border-bottom: 1px solid #ddd;">AI Rec.</th>
                                <th style="padding: 10px; text-align: left; border-bottom: 1px solid #ddd;">Followed</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            game.moves.forEach((move, index) => {
                const player = move.isPlayerMove ? `You (${move.symbol})` : `Opponent (${move.symbol})`;
                const position = `${move.position + 1} (${move.row + 1},${move.col + 1})`;
                const aiRec = move.aiRecommendation !== null ? `${move.aiRecommendation + 1}` : '-';
                const followed = move.isPlayerMove && move.aiRecommendation !== null ?
                               (move.followedRecommendation ? '✅' : '❌') : '-';

                html += `
                    <tr style="border-bottom: 1px solid #eee;">
                        <td style="padding: 8px;">${index + 1}</td>
                        <td style="padding: 8px;">${player}</td>
                        <td style="padding: 8px;">${position}</td>
                        <td style="padding: 8px;">${aiRec}</td>
                        <td style="padding: 8px;">${followed}</td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>

                <div style="margin-top: 20px; text-align: center;">
                    <button onclick="exportGame(${gameIndex})" style="background: #4CAF50; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-right: 10px;">
                        Export This Game
                    </button>
                </div>
            `;

            return html;
        }

        // Export Functions
        function exportCurrentGame() {
            if (currentGame) {
                exportGame(0, currentGame);
            }
        }

        function exportGame(gameIndex, gameData = null) {
            const game = gameData || gameHistory[gameIndex];
            if (!game) return;

            const exportText = generateExportText(game);

            // Create a temporary textarea to copy text
            const textarea = document.createElement('textarea');
            textarea.value = exportText;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);

            // Show confirmation
            alert('Game data copied to clipboard! You can now paste it anywhere to share or analyze.');
        }

        function generateExportText(game) {
            const outcome = game.outcome === 'win' ? 'VICTORY' :
                          game.outcome === 'loss' ? 'DEFEAT' : 'DRAW';

            let text = `
=== 7x7 TIC-TAC-TOE GAME ANALYSIS ===
Game ID: ${game.id}
Date: ${new Date(game.startTime).toLocaleString()}
Duration: ${Math.round(game.duration / 1000)} seconds
Outcome: ${outcome}
Winner: ${game.winner || 'None (Draw)'}

GAME SETUP:
- Player Symbol: ${game.playerSymbol}
- Opponent Symbol: ${game.opponentSymbol}
- First Player: ${game.playerGoesFirst ? 'Player' : 'Opponent'}
- Total Moves: ${game.moves.length}

CRITICAL ANALYSIS:
`;

            if (game.criticalMoves.length > 0) {
                game.criticalMoves.forEach(critical => {
                    if (critical.type === 'deviation') {
                        text += `- Move ${critical.moveIndex + 1}: Player deviated from AI recommendation\n`;
                        text += `  Player chose: Position ${critical.playerMove + 1} | AI suggested: Position ${critical.recommendedMove + 1}\n`;
                    } else if (critical.type === 'losing_sequence') {
                        text += `- ${critical.description}\n`;
                        text += `  Key moves: ${critical.moves.map(m => `Position ${m + 1}`).join(', ')}\n`;
                    }
                });
            } else {
                text += '- No critical deviations detected\n';
            }

            text += `\nMOVE HISTORY:\n`;
            text += `Move# | Player    | Position | Row,Col | AI Rec. | Followed | Time\n`;
            text += `------|-----------|----------|---------|---------|----------|----------\n`;

            game.moves.forEach((move, index) => {
                const player = move.isPlayerMove ? `Player(${move.symbol})` : `Opponent(${move.symbol})`;
                const position = `${move.position + 1}`.padStart(8);
                const rowCol = `${move.row + 1},${move.col + 1}`.padStart(7);
                const aiRec = move.aiRecommendation !== null ? `${move.aiRecommendation + 1}`.padStart(7) : '   -   ';
                const followed = move.isPlayerMove && move.aiRecommendation !== null ?
                               (move.followedRecommendation ? '   ✓   ' : '   ✗   ') : '   -   ';
                const time = new Date(move.timestamp).toLocaleTimeString();

                text += `${(index + 1).toString().padStart(5)} | ${player.padEnd(9)} | ${position} | ${rowCol} | ${aiRec} | ${followed} | ${time}\n`;
            });

            text += `\nFINAL BOARD STATE:\n`;
            text += generateBoardVisualization(game.finalBoard);

            text += `\nAI RECOMMENDATIONS LOG:\n`;
            game.aiRecommendations.forEach((rec, index) => {
                text += `${index + 1}. Position ${rec.position + 1} (${rec.row + 1},${rec.col + 1}) at ${new Date(rec.timestamp).toLocaleTimeString()}\n`;
                if (rec.reasoning) {
                    text += `   Reasoning: ${rec.reasoning}\n`;
                }
            });

            text += `\n=== END OF ANALYSIS ===\n`;
            text += `Generated by 7x7 Tic-Tac-Toe Training Assistant\n`;
            text += `Export Time: ${new Date().toLocaleString()}\n`;

            return text;
        }

        function generateBoardVisualization(boardState) {
            let visual = '';
            for (let row = 0; row < 7; row++) {
                let line = '';
                for (let col = 0; col < 7; col++) {
                    const pos = row * 7 + col;
                    const symbol = boardState[pos] || ' ';
                    line += `[${symbol}]`;
                }
                visual += line + '\n';
            }
            return visual;
        }

        // Initialize the game when page loads
        window.onload = function() {
            initializeBoard();
            loadGameHistory();
        };
    </script>
</body>
</html>
